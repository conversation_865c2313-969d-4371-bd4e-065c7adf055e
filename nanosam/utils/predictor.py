# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from torch2trt import TRTModule
from typing import Tuple
import tensorrt as trt
import PIL.Image
import torch
import numpy as np
import torch.nn.functional as F

def load_mask_decoder_engine(path: str):
    
    with trt.Logger() as logger, trt.Runtime(logger) as runtime:
        with open(path, 'rb') as f:
            engine_bytes = f.read()
        engine = runtime.deserialize_cuda_engine(engine_bytes)

    mask_decoder_trt = TRTModule(
        engine=engine,
        input_names=[
            "image_embeddings",
            "point_coords",
            "point_labels",
            "mask_input",
            "has_mask_input"
        ],
        output_names=[
            "iou_predictions",
            "low_res_masks"
        ]
    )

    return mask_decoder_trt


def load_image_encoder_engine(path: str):

    with trt.Logger() as logger, trt.Runtime(logger) as runtime:
        with open(path, 'rb') as f:
            engine_bytes = f.read()
        engine = runtime.deserialize_cuda_engine(engine_bytes)

    image_encoder_trt = TRTModule(
        engine=engine,
        input_names=["image"],
        output_names=["image_embeddings"]
    )

    return image_encoder_trt


def preprocess_image(image, size: int = 512):

    if isinstance(image, np.ndarray):
        image = PIL.Image.fromarray(image)

    image_mean = torch.tensor([123.675, 116.28, 103.53])[:, None, None]
    image_std = torch.tensor([58.395, 57.12, 57.375])[:, None, None]

    image_pil = image
    aspect_ratio = image_pil.width / image_pil.height
    if aspect_ratio >= 1:
        resize_width = size
        resize_height = int(size / aspect_ratio)
    else:
        resize_height = size
        resize_width = int(size * aspect_ratio)

    image_pil_resized = image_pil.resize((resize_width, resize_height))
    image_np_resized = np.asarray(image_pil_resized)
    # to writable
    image_np_resized = image_np_resized.copy()
    image_torch_resized = torch.from_numpy(image_np_resized).permute(2, 0, 1)
    image_torch_resized_normalized = (image_torch_resized.float() - image_mean) / image_std
    image_tensor = torch.zeros((1, 3, size, size))
    image_tensor[0, :, :resize_height, :resize_width] = image_torch_resized_normalized

    return image_tensor.cuda()


def preprocess_points(points, image_size, size: int = 1024):
    scale = size / max(*image_size)
    points = points * scale
    return points


def run_mask_decoder(mask_decoder_engine, features, points=None, point_labels=None, mask_input=None):
    if points is not None:
        assert point_labels is not None
        assert len(points) == len(point_labels)
    
    points = np.array([points])  
    image_point_coords = torch.from_numpy(points).float().cuda()
    point_labels = np.array([point_labels]) 
    image_point_labels = torch.from_numpy(point_labels).float().cuda()
    
    # image_point_coords = torch.tensor([points]).float().cuda()
    # image_point_labels = torch.tensor([point_labels]).float().cuda()

    if mask_input is None:
        mask_input = torch.zeros(1, 1, 256, 256).float().cuda()
        has_mask_input = torch.tensor([0]).float().cuda()
    else:
        has_mask_input = torch.tensor([1]).float().cuda()


    iou_predictions, low_res_masks = mask_decoder_engine(
        features,
        image_point_coords,
        image_point_labels,
        mask_input,
        has_mask_input
    )

    return iou_predictions, low_res_masks


def upscale_mask(mask, image_shape, size=256):
    
    if image_shape[1] > image_shape[0]:
        lim_x = size
        lim_y = int(size * image_shape[0] / image_shape[1])
    else:
        lim_x = int(size * image_shape[1] / image_shape[0])
        lim_y = size

    mask[:, :, :lim_y, :lim_x]
    mask = F.interpolate(mask[:, :, :lim_y, :lim_x], image_shape, mode='bilinear')
    
    return mask


class Predictor(object):

    def __init__(self,
            image_encoder_engine: str,
            mask_decoder_engine: str,
            image_encoder_size: int = 1024,
            orig_image_encoder_size: int = 1024,
        ):
        self.image_encoder_engine = load_image_encoder_engine(image_encoder_engine)
        self.mask_decoder_engine = load_mask_decoder_engine(mask_decoder_engine)
        self.image_encoder_size = image_encoder_size
        self.orig_image_encoder_size = orig_image_encoder_size

    def set_image(self, image):
        self.image = image
        self.image_tensor = preprocess_image(image, self.image_encoder_size)
        self.features = self.image_encoder_engine(self.image_tensor)

    def predict(self, points, point_labels, mask_input=None):
        points = preprocess_points(
            points, 
            (self.image.height, self.image.width),
            self.orig_image_encoder_size
        )
        mask_iou, low_res_mask = run_mask_decoder(
            self.mask_decoder_engine,
            self.features,
            points,
            point_labels,
            mask_input
        )

        hi_res_mask = upscale_mask(
            low_res_mask, 
            (self.image.height, self.image.width)                           
        )

        return hi_res_mask, mask_iou, low_res_mask


from nanosam.utils.utils_amg import (
    build_all_layer_point_grids,
    batch_iterator,
    MaskData,
    generate_crop_boxes,
    batched_mask_to_box,
    mask_to_rle_pytorch,
    calculate_stability_score,
    rle_to_mask,
    uncrop_boxes_xyxy, 
    uncrop_points
)

class AutomaticPredictor(object):
    """
    TensorRT-based automatic mask generator that generates masks for an entire image
    without requiring manual prompts.
    """

    def __init__(self,
            image_encoder_engine: str,
            mask_decoder_engine: str,
            image_encoder_size: int = 1024,
            orig_image_encoder_size: int = 1024,
            points_per_side: int = 32,
            points_per_batch: int = 64,
            pred_iou_thresh: float = 0.88,
            stability_score_thresh: float = 0.95,
            box_nms_thresh: float = 0.7,
            crop_n_layers: int = 0,
            crop_overlap_ratio: float = 512 / 1500,
        ):
        """
        Initialize the AutomaticPredictor with TensorRT engines.

        Args:
            image_encoder_engine: Path to TensorRT image encoder engine
            mask_decoder_engine: Path to TensorRT mask decoder engine
            image_encoder_size: Input size for image encoder
            orig_image_encoder_size: Original image encoder size for coordinate scaling
            points_per_side: Number of points per side for point grid generation
            points_per_batch: Number of points to process in each batch
            pred_iou_thresh: IoU threshold for filtering predictions
            stability_score_thresh: Stability score threshold for filtering
            box_nms_thresh: NMS threshold for removing duplicate boxes
            crop_n_layers: Number of crop layers (0 means no cropping)
            crop_overlap_ratio: Overlap ratio between crops
        """
        # Initialize TensorRT engines
        self.image_encoder_engine = load_image_encoder_engine(image_encoder_engine)
        self.mask_decoder_engine = load_mask_decoder_engine(mask_decoder_engine)
        self.image_encoder_size = image_encoder_size
        self.orig_image_encoder_size = orig_image_encoder_size

        # Automatic mask generation parameters
        self.points_per_side = points_per_side
        self.points_per_batch = points_per_batch
        self.pred_iou_thresh = pred_iou_thresh
        self.stability_score_thresh = stability_score_thresh
        self.box_nms_thresh = box_nms_thresh
        self.crop_n_layers = crop_n_layers
        self.crop_overlap_ratio = crop_overlap_ratio

        # Import required utilities
        # from ..mobile_sam.utils.amg import (
        #     build_all_layer_point_grids,
        #     batch_iterator,
        #     MaskData,
        #     generate_crop_boxes,
        #     batched_mask_to_box,
        #     mask_to_rle_pytorch,
        #     calculate_stability_score,
        #     remove_small_regions,
        #     rle_to_mask
        # )

        self.build_all_layer_point_grids = build_all_layer_point_grids
        self.batch_iterator = batch_iterator
        self.MaskData = MaskData
        self.generate_crop_boxes = generate_crop_boxes
        self.batched_mask_to_box = batched_mask_to_box
        self.mask_to_rle_pytorch = mask_to_rle_pytorch
        self.calculate_stability_score = calculate_stability_score
        self.rle_to_mask = rle_to_mask

        # Generate point grids
        self.point_grids = self.build_all_layer_point_grids(
            points_per_side,
            crop_n_layers,
            1  # crop_n_points_downscale_factor
        )

    def generate_masks(self, image):
        """
        Generate masks for the entire image automatically.

        Args:
            image: PIL Image or numpy array in HWC format

        Returns:
            List of dictionaries containing mask data
        """
        # Convert PIL image to numpy if needed
        if hasattr(image, 'convert'):
            image_np = np.array(image.convert('RGB'))
        else:
            image_np = image

        # Set image for feature extraction
        self.image = image if hasattr(image, 'height') else PIL.Image.fromarray(image_np)
        self.image_tensor = preprocess_image(self.image, self.image_encoder_size)
        self.features = self.image_encoder_engine(self.image_tensor)

        # Generate masks
        mask_data = self._generate_masks(image_np)

        # Convert to output format
        results = []
        for i in range(len(mask_data["rles"])):
            mask = self.rle_to_mask(mask_data["rles"][i])
            result = {
                "segmentation": mask,
                "area": int(mask.sum()),
                "bbox": mask_data["boxes"][i].tolist(),
                "predicted_iou": float(mask_data["iou_preds"][i]),
                "point_coords": mask_data["points"][i].tolist(),
                "stability_score": float(mask_data["stability_scores"][i]),
                "crop_box": mask_data["crop_boxes"][i].tolist(),
            }
            results.append(result)

        return results

    def _generate_masks(self, image):
        """Generate masks using point grid sampling."""
        orig_size = image.shape[:2]
        crop_boxes, layer_idxs = self.generate_crop_boxes(
            orig_size, self.crop_n_layers, self.crop_overlap_ratio
        )

        # Process each crop
        data = self.MaskData()
        for crop_box, layer_idx in zip(crop_boxes, layer_idxs):
            crop_data = self._process_crop(image, crop_box, layer_idx)
            data.cat(crop_data)

        # Remove duplicates if multiple crops
        if len(crop_boxes) > 1:
            from torchvision.ops.boxes import batched_nms, box_area
            scores = 1 / box_area(data["crop_boxes"])
            scores = scores.to(data["boxes"].device)
            keep_by_nms = batched_nms(
                data["boxes"].float(),
                scores,
                torch.zeros_like(data["boxes"][:, 0]),
                iou_threshold=self.box_nms_thresh,
            )
            data.filter(keep_by_nms)

        data.to_numpy()
        return data

    def _process_crop(self, image, crop_box, crop_layer_idx):
        """Process a single crop of the image."""
        # Crop the image
        x0, y0, x1, y1 = crop_box
        cropped_im = image[y0:y1, x0:x1, :]
        cropped_im_size = cropped_im.shape[:2]

        # Set cropped image for feature extraction
        cropped_pil = PIL.Image.fromarray(cropped_im)
        cropped_tensor = preprocess_image(cropped_pil, self.image_encoder_size)
        cropped_features = self.image_encoder_engine(cropped_tensor)

        # Get points for this crop
        points_scale = np.array(cropped_im_size)[None, ::-1]
        points_for_image = self.point_grids[crop_layer_idx] * points_scale

        # Generate masks in batches
        data = self.MaskData()
        for (points,) in self.batch_iterator(self.points_per_batch, points_for_image):
            batch_data = self._process_batch(points, cropped_im_size, cropped_features)
            data.cat(batch_data)

        # Remove duplicates within this crop
        from torchvision.ops.boxes import batched_nms
        keep_by_nms = batched_nms(
            data["boxes"].float(),
            data["iou_preds"],
            torch.zeros_like(data["boxes"][:, 0]),
            iou_threshold=self.box_nms_thresh,
        )
        data.filter(keep_by_nms)

        # Return to original image coordinates
        # from ..mobile_sam.utils.amg import uncrop_boxes_xyxy, uncrop_points
        data["boxes"] = uncrop_boxes_xyxy(data["boxes"], crop_box)
        data["points"] = uncrop_points(data["points"], crop_box)
        data["crop_boxes"] = torch.tensor([crop_box for _ in range(len(data["rles"]))])

        return data

    def _process_batch(self, points, im_size, features):
        """Process a batch of points to generate masks."""
        # Preprocess points for TensorRT model
        points_processed = preprocess_points(
            points,
            im_size,
            self.orig_image_encoder_size
        )

        # Generate masks for each point
        masks_list = []
        iou_preds_list = []
        points_list = []

        for point in points_processed:
            # Run TensorRT mask decoder for single point
            point_labels = np.array([1])  # Foreground point
            mask_iou, low_res_mask = run_mask_decoder(
                self.mask_decoder_engine,
                features,
                [point],
                point_labels,
                None
            )

            # Upscale mask
            hi_res_mask = upscale_mask(
                low_res_mask,
                im_size
            )

            # Store results (take best mask based on IoU)
            best_idx = int(mask_iou.argmax())
            masks_list.append(hi_res_mask[:, best_idx:best_idx+1, :, :])
            iou_preds_list.append(mask_iou[:, best_idx])
            points_list.append(point)

        # Stack results
        masks = torch.cat(masks_list, dim=1)  # [1, N, H, W]
        iou_preds = torch.cat(iou_preds_list, dim=0)  # [N]
        points_tensor = torch.tensor(np.array(points_list))  # [N, 2]

        # Ensure all tensors are on the same device (GPU if available)
        device = iou_preds.device
        points_tensor = points_tensor.to(device)

        # Filter by IoU threshold
        keep_mask = iou_preds > self.pred_iou_thresh
        masks = masks[:, keep_mask, :, :]
        iou_preds = iou_preds[keep_mask]
        points_tensor = points_tensor[keep_mask]

        if masks.shape[1] == 0:
            # No masks passed threshold
            return self.MaskData()

        # Calculate stability scores
        stability_scores = self.calculate_stability_score(
            masks[0], 0.0, 1.0
        )

        # Filter by stability score
        keep_mask = stability_scores > self.stability_score_thresh
        masks = masks[:, keep_mask, :, :]
        iou_preds = iou_preds[keep_mask]
        points_tensor = points_tensor[keep_mask]
        stability_scores = stability_scores[keep_mask]

        if masks.shape[1] == 0:
            # No masks passed stability threshold
            return self.MaskData()

        # Convert masks to RLE and calculate boxes
        masks_np = masks[0].detach().cpu().numpy()  # [N, H, W]
        rles = []
        boxes = []

        for i in range(masks_np.shape[0]):
            mask = masks_np[i] > 0
            rle = self.mask_to_rle_pytorch(torch.tensor(mask, device='cpu').unsqueeze(0))[0]
            rles.append(rle)

            # Calculate bounding box
            if mask.any():
                y_indices, x_indices = np.where(mask)
                x_min, x_max = x_indices.min(), x_indices.max()
                y_min, y_max = y_indices.min(), y_indices.max()
                boxes.append([x_min, y_min, x_max, y_max])
            else:
                boxes.append([0, 0, 0, 0])

        # Create MaskData - ensure all tensors are on CPU for final output
        data = self.MaskData(
            rles=rles,
            boxes=torch.tensor(boxes).float(),
            iou_preds=iou_preds.detach().cpu(),
            points=points_tensor.detach().cpu(),
            stability_scores=stability_scores.detach().cpu(),
        )

        return data


class HierarchicalPredictor(object):
    """
    TensorRT-based hierarchical mask generator that generates masks for an entire image
    using a two-stage adaptive sampling strategy.
    """

    def __init__(self,
            image_encoder_engine: str,
            mask_decoder_engine: str,
            image_encoder_size: int = 1024,
            orig_image_encoder_size: int = 1024,
            points_per_side: int = 32,
            points_per_batch: int = 64,
            pred_iou_thresh: float = 0.88,
            high_score_thresh: float = 8.5,
            stability_score_thresh: float = 0.95,
            box_nms_thresh: float = 0.7,
        ):
        """
        Initialize the HierarchicalPredictor with TensorRT engines.

        Args:
            image_encoder_engine: Path to TensorRT image encoder engine
            mask_decoder_engine: Path to TensorRT mask decoder engine
            image_encoder_size: Input size for image encoder
            orig_image_encoder_size: Original image encoder size for coordinate scaling
            points_per_side: Number of points per side for point grid generation
            points_per_batch: Number of points to process in each batch
            pred_iou_thresh: IoU threshold for filtering predictions
            high_score_thresh: High score threshold for identifying unmasked areas
            stability_score_thresh: Stability score threshold for filtering
            box_nms_thresh: NMS threshold for removing duplicate boxes
        """
        # Initialize TensorRT engines
        self.image_encoder_engine = load_image_encoder_engine(image_encoder_engine)
        self.mask_decoder_engine = load_mask_decoder_engine(mask_decoder_engine)
        self.image_encoder_size = image_encoder_size
        self.orig_image_encoder_size = orig_image_encoder_size

        # Hierarchical mask generation parameters
        self.points_per_side = points_per_side
        self.points_per_batch = points_per_batch
        self.pred_iou_thresh = pred_iou_thresh
        self.high_score_thresh = high_score_thresh
        self.stability_score_thresh = stability_score_thresh
        self.box_nms_thresh = box_nms_thresh

        # Import required utilities
        self.build_all_layer_point_grids = build_all_layer_point_grids
        self.batch_iterator = batch_iterator
        self.MaskData = MaskData
        self.batched_mask_to_box = batched_mask_to_box
        self.mask_to_rle_pytorch = mask_to_rle_pytorch
        self.calculate_stability_score = calculate_stability_score
        self.rle_to_mask = rle_to_mask

        # Initialize point grids (will be updated dynamically)
        self.point_grids = None

    def generate_masks(self, image):
        """
        Generate masks for the entire image using hierarchical strategy.

        Args:
            image: PIL Image or numpy array in HWC format

        Returns:
            List of dictionaries containing mask data
        """
        # Convert PIL image to numpy if needed
        if hasattr(image, 'convert'):
            image_np = np.array(image.convert('RGB'))
        else:
            image_np = image

        # Set image for feature extraction
        self.image = image if hasattr(image, 'height') else PIL.Image.fromarray(image_np)
        self.image_tensor = preprocess_image(self.image, self.image_encoder_size)
        self.features = self.image_encoder_engine(self.image_tensor)

        # Generate masks using hierarchical strategy
        mask_data = self._hierarchical_generate(image_np)

        # Convert to output format
        results = []
        for i in range(len(mask_data["rles"])):
            mask = self.rle_to_mask(mask_data["rles"][i])
            result = {
                "segmentation": mask,
                "area": int(mask.sum()),
                "bbox": mask_data["boxes"][i].tolist(),
                "predicted_iou": float(mask_data["iou_preds"][i]),
                "point_coords": mask_data["points"][i].tolist(),
                "stability_score": float(mask_data["stability_scores"][i]),
                # Note: No crop_box field for hierarchical approach
            }
            results.append(result)

        return results

    def _hierarchical_generate(self, image):
        """Generate masks using hierarchical two-stage strategy."""
        orig_size = image.shape[:2]
        ih, iw = orig_size

        # Stage 1: Generate initial masks with sparse point grid
        sparse_points_per_side = self.points_per_side // 4
        self.point_grids = self.build_all_layer_point_grids(
            sparse_points_per_side, 0, 1
        )

        # Generate initial masks and high confidence mask
        initial_data, high_confidence_mask = self._generate_stage(image, need_high=True)

        # Stage 2: Generate additional points based on uncovered areas
        hstride = ih // self.points_per_side
        wstride = iw // self.points_per_side
        new_points = []

        # Convert high confidence mask to CPU numpy for processing
        if isinstance(high_confidence_mask, torch.Tensor):
            high_confidence_np = high_confidence_mask.detach().cpu().numpy()
        else:
            high_confidence_np = high_confidence_mask

        # Generate new sampling points around uncovered areas
        full_point_grids = np.array(self.point_grids)

        for mask_idx in range(full_point_grids.shape[1]):
            point_coords = [
                full_point_grids[0, mask_idx, 0] * iw,
                full_point_grids[0, mask_idx, 1] * ih
            ]

            # Check 3x3 neighborhood around each initial point
            for sy in [-1, 0, 1]:
                for sx in [-1, 0, 1]:
                    if sy == 0 and sx == 0:
                        continue

                    new_x = int(point_coords[0] + wstride * sx)
                    new_y = int(point_coords[1] + hstride * sy)

                    # Check bounds and if area is uncovered
                    if (0 <= new_x < iw and 0 <= new_y < ih and
                        not high_confidence_np[new_y, new_x]):
                        new_points.append([new_x / iw, new_y / ih])

            # Check additional points at distance 2
            for direction in [(2, 0), (0, 2), (2, 2)]:
                dx, dy = direction
                new_x = int(point_coords[0] + wstride * dx)
                new_y = int(point_coords[1] + hstride * dy)

                if (new_x < iw and new_y < ih and
                    not high_confidence_np[new_y, new_x]):
                    new_points.append([new_x / iw, new_y / ih])

        # Stage 2: Generate masks for new points
        if new_points:
            self.point_grids = [np.array(new_points)]
            second_stage_data = self._generate_stage(image, need_high=False)

            # Combine data from both stages
            initial_data.cat(second_stage_data)

        # Post-process combined data
        final_data = self._post_process(initial_data, orig_size)

        return final_data

    def _generate_stage(self, image, need_high=False):
        """Generate masks for a single stage."""
        orig_size = image.shape[:2]

        # Get points for this stage
        points_scale = np.array(orig_size)[None, ::-1]
        points_for_image = self.point_grids[0] * points_scale

        # Generate masks in batches
        data = self.MaskData()
        high_confidence_masks = []

        for (points,) in self.batch_iterator(self.points_per_batch, points_for_image):
            batch_data = self._process_batch_hierarchical(points, orig_size, need_high)

            # Check if high_masks exists using the internal _stats dict
            if need_high and "high_masks" in batch_data._stats:
                high_confidence_masks.extend(batch_data._stats["high_masks"])
                del batch_data._stats["high_masks"]

            data.cat(batch_data)

        if need_high:
            # Combine all high confidence masks
            if high_confidence_masks:
                combined_high_mask = torch.zeros(orig_size, dtype=torch.bool)
                for mask in high_confidence_masks:
                    if isinstance(mask, torch.Tensor):
                        mask_2d = mask.squeeze()
                        if mask_2d.dim() == 2:
                            combined_high_mask = torch.logical_or(combined_high_mask, mask_2d)
                return data, combined_high_mask
            else:
                return data, torch.zeros(orig_size, dtype=torch.bool)
        else:
            return data

    def _process_batch_hierarchical(self, points, im_size, need_high=False):
        """Process a batch of points for hierarchical generation."""
        # Preprocess points for TensorRT model
        points_processed = preprocess_points(
            points,
            im_size,
            self.orig_image_encoder_size
        )

        # Generate masks for each point
        masks_list = []
        iou_preds_list = []
        points_list = []
        high_masks_list = []

        for point in points_processed:
            # Run TensorRT mask decoder for single point
            point_labels = np.array([1])  # Foreground point
            mask_iou, low_res_mask = run_mask_decoder(
                self.mask_decoder_engine,
                self.features,
                [point],
                point_labels,
                None
            )

            # Upscale mask
            hi_res_mask = upscale_mask(
                low_res_mask,
                im_size
            )

            # Store results (take best mask based on IoU)
            best_idx = int(mask_iou.argmax())
            final_mask = hi_res_mask[:, best_idx:best_idx+1, :, :]
            masks_list.append(final_mask)
            iou_preds_list.append(mask_iou[:, best_idx])
            points_list.append(point)

            # Store high confidence mask if needed
            if need_high:
                high_mask = final_mask > self.high_score_thresh
                high_masks_list.append(high_mask.squeeze())

        # Stack results
        masks = torch.cat(masks_list, dim=1)  # [1, N, H, W]
        iou_preds = torch.cat(iou_preds_list, dim=0)  # [N]
        points_tensor = torch.tensor(np.array(points_list))  # [N, 2]

        # Ensure all tensors are on the same device
        device = iou_preds.device
        points_tensor = points_tensor.to(device)

        # Filter by IoU threshold
        keep_mask = iou_preds > self.pred_iou_thresh
        masks = masks[:, keep_mask, :, :]
        iou_preds = iou_preds[keep_mask]
        points_tensor = points_tensor[keep_mask]

        if need_high:
            high_masks_list = [high_masks_list[i] for i in range(len(high_masks_list)) if keep_mask[i]]

        if masks.shape[1] == 0:
            # No masks passed threshold
            result = self.MaskData()
            if need_high:
                result._stats["high_masks"] = []
            return result

        # Calculate stability scores
        stability_scores = self.calculate_stability_score(
            masks[0], 0.0, 1.0
        )

        # Filter by stability score
        keep_mask = stability_scores > self.stability_score_thresh
        masks = masks[:, keep_mask, :, :]
        iou_preds = iou_preds[keep_mask]
        points_tensor = points_tensor[keep_mask]
        stability_scores = stability_scores[keep_mask]

        if need_high:
            high_masks_list = [high_masks_list[i] for i in range(len(high_masks_list)) if keep_mask[i]]

        if masks.shape[1] == 0:
            # No masks passed stability threshold
            result = self.MaskData()
            if need_high:
                result._stats["high_masks"] = []
            return result

        # Convert masks to RLE and calculate boxes
        masks_np = masks[0].detach().cpu().numpy()  # [N, H, W]
        rles = []
        boxes = []

        for i in range(masks_np.shape[0]):
            mask = masks_np[i] > 0
            rle = self.mask_to_rle_pytorch(torch.tensor(mask, device='cpu').unsqueeze(0))[0]
            rles.append(rle)

            # Calculate bounding box
            if mask.any():
                y_indices, x_indices = np.where(mask)
                x_min, x_max = x_indices.min(), x_indices.max()
                y_min, y_max = y_indices.min(), y_indices.max()
                boxes.append([x_min, y_min, x_max, y_max])
            else:
                boxes.append([0, 0, 0, 0])

        # Create MaskData
        data = self.MaskData(
            rles=rles,
            boxes=torch.tensor(boxes).float(),
            iou_preds=iou_preds.detach().cpu(),
            points=points_tensor.detach().cpu(),
            stability_scores=stability_scores.detach().cpu(),
        )

        if need_high:
            # Store high_masks directly in the internal _stats dict to avoid type checking
            data._stats["high_masks"] = high_masks_list

        return data

    def _post_process(self, data, orig_size):
        """Post-process the combined mask data."""
        if len(data["rles"]) == 0:
            return data

        # Apply NMS to remove duplicates
        from torchvision.ops.boxes import batched_nms
        keep_by_nms = batched_nms(
            data["boxes"].float(),
            data["iou_preds"],
            torch.zeros_like(data["boxes"][:, 0]),  # categories
            iou_threshold=self.box_nms_thresh,
        )
        data.filter(keep_by_nms)

        # Convert to numpy for final output
        data.to_numpy()

        return data