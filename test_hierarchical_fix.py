#!/usr/bin/env python3

"""
Test script to verify the HierarchicalPredictor fixes for MaskData key access.
This script tests the MaskData key checking without requiring actual TensorRT engines.
"""

import sys
import os
import numpy as np

# Add the nanosam directory to the path
sys.path.insert(0, '/home/<USER>/shared/DevCode/nanosam')

def test_maskdata_key_access():
    """Test MaskData key access patterns."""
    
    try:
        from nanosam.utils.utils_amg import MaskData
        
        # Test 1: Basic MaskData creation and access
        print("🧪 Testing basic MaskData operations...")
        
        data = MaskData(test_key=[1, 2, 3])
        
        # Test direct access
        assert data["test_key"] == [1, 2, 3], "Direct access failed"
        print("✅ Direct access works")
        
        # Test key existence check using _stats
        assert "test_key" in data._stats, "Key existence check failed"
        print("✅ Key existence check using _stats works")
        
        # Test non-existent key
        assert "non_existent" not in data._stats, "Non-existent key check failed"
        print("✅ Non-existent key check works")
        
        # Test 2: Test the problematic pattern from our code
        print("\n🧪 Testing hierarchical pattern...")
        
        # Simulate the pattern used in HierarchicalPredictor
        batch_data = MaskData(
            rles=[],
            boxes=np.array([]),
            iou_preds=np.array([]),
            points=np.array([]),
            stability_scores=np.array([]),
        )
        
        # Add high_masks using _stats (our fix)
        batch_data._stats["high_masks"] = []
        
        # Test the check pattern
        need_high = True
        if need_high and "high_masks" in batch_data._stats:
            print("✅ High masks key check works")
            high_masks = batch_data._stats["high_masks"]
            assert high_masks == [], "High masks retrieval failed"
            print("✅ High masks retrieval works")
        else:
            print("❌ High masks key check failed")
            return False
        
        # Test 3: Test deletion
        del batch_data._stats["high_masks"]
        assert "high_masks" not in batch_data._stats, "Deletion failed"
        print("✅ High masks deletion works")
        
        return True
        
    except Exception as e:
        print(f"❌ MaskData test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hierarchical_predictor_import():
    """Test that HierarchicalPredictor can be imported without syntax errors."""
    
    try:
        # This will fail due to missing dependencies, but should not have syntax errors
        from nanosam.utils.predictor import HierarchicalPredictor
        print("❌ Import succeeded unexpectedly (dependencies should be missing)")
        return False
    except ModuleNotFoundError as e:
        if "torch2trt" in str(e):
            print("✅ HierarchicalPredictor import has correct syntax (dependency error expected)")
            return True
        else:
            print(f"❌ Unexpected import error: {e}")
            return False
    except SyntaxError as e:
        print(f"❌ Syntax error in HierarchicalPredictor: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error importing HierarchicalPredictor: {e}")
        return False

def test_hierarchical_usage_script():
    """Test that the hierarchical usage script has correct syntax."""
    
    try:
        import ast
        
        script_path = '/home/<USER>/shared/DevCode/nanosam/examples/hierarchical_usage.py'
        
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Parse the script to check for syntax errors
        ast.parse(content)
        print("✅ hierarchical_usage.py has correct syntax")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in hierarchical_usage.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking hierarchical_usage.py: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing HierarchicalPredictor fixes...")
    print("=" * 60)
    
    tests = [
        ("MaskData Key Access", test_maskdata_key_access),
        ("HierarchicalPredictor Import", test_hierarchical_predictor_import),
        ("Hierarchical Usage Script", test_hierarchical_usage_script),
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing {test_name}:")
        print("-" * 40)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests PASSED! The KeyError fix should work.")
        print("\nThe original error was caused by:")
        print("1. Using 'key in batch_data' instead of 'key in batch_data._stats'")
        print("2. MaskData class lacks __contains__ method")
        print("3. Python fallback to __getitem__ caused the KeyError")
        print("\nFix applied:")
        print("1. Use 'key in batch_data._stats' for key existence checks")
        print("2. Use 'batch_data._stats[key]' for direct access to avoid type checking")
    else:
        print("❌ Some tests FAILED. Please review the fixes.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
