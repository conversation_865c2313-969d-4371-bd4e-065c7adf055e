#!/usr/bin/env python3

"""
Test script to verify the HierarchicalPredictor interface matches the expected pattern.
This script tests the interface without requiring actual TensorRT engines.
"""

import sys
import os
import ast
import inspect

# Add the nanosam directory to the path
sys.path.insert(0, '/home/<USER>/shared/DevCode/nanosam')

def test_class_interface():
    """Test that HierarchicalPredictor has the expected interface."""
    
    # Read the predictor.py file and parse it
    with open('/home/<USER>/shared/DevCode/nanosam/nanosam/utils/predictor.py', 'r') as f:
        content = f.read()
    
    # Parse the AST to find class definitions
    tree = ast.parse(content)
    
    hierarchical_class = None
    automatic_class = None
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            if node.name == 'HierarchicalPredictor':
                hierarchical_class = node
            elif node.name == 'AutomaticPredictor':
                automatic_class = node
    
    if not hierarchical_class:
        print("❌ HierarchicalPredictor class not found!")
        return False
    
    if not automatic_class:
        print("❌ AutomaticPredictor class not found!")
        return False
    
    print("✅ Both HierarchicalPredictor and AutomaticPredictor classes found")
    
    # Check that both classes have the required methods
    hierarchical_methods = [node.name for node in hierarchical_class.body if isinstance(node, ast.FunctionDef)]
    automatic_methods = [node.name for node in automatic_class.body if isinstance(node, ast.FunctionDef)]
    
    print(f"HierarchicalPredictor methods: {hierarchical_methods}")
    print(f"AutomaticPredictor methods: {automatic_methods}")
    
    # Both should have __init__ and generate_masks
    required_methods = ['__init__', 'generate_masks']
    
    for method in required_methods:
        if method not in hierarchical_methods:
            print(f"❌ HierarchicalPredictor missing required method: {method}")
            return False
        if method not in automatic_methods:
            print(f"❌ AutomaticPredictor missing required method: {method}")
            return False
    
    print("✅ Both classes have required methods: __init__, generate_masks")
    
    # Check constructor parameters
    hierarchical_init = None
    automatic_init = None
    
    for node in hierarchical_class.body:
        if isinstance(node, ast.FunctionDef) and node.name == '__init__':
            hierarchical_init = node
            break
    
    for node in automatic_class.body:
        if isinstance(node, ast.FunctionDef) and node.name == '__init__':
            automatic_init = node
            break
    
    if hierarchical_init and automatic_init:
        h_args = [arg.arg for arg in hierarchical_init.args.args[1:]]  # Skip 'self'
        a_args = [arg.arg for arg in automatic_init.args.args[1:]]     # Skip 'self'
        
        print(f"HierarchicalPredictor __init__ args: {h_args}")
        print(f"AutomaticPredictor __init__ args: {a_args}")
        
        # Check for hierarchical-specific parameter
        if 'high_score_thresh' in h_args:
            print("✅ HierarchicalPredictor has hierarchical-specific parameter: high_score_thresh")
        else:
            print("❌ HierarchicalPredictor missing hierarchical-specific parameter: high_score_thresh")
            return False
    
    return True

def test_output_format():
    """Test that the expected output format is documented correctly."""
    
    # Read the algorithm analysis file
    with open('/home/<USER>/shared/DevCode/nanosam/nanosam/mobile_sam/algorithm_analysis.md', 'r') as f:
        content = f.read()
    
    # Check that the differences are documented
    if 'crop_box' in content and 'HierarchicalMaskGenerator' in content:
        print("✅ Algorithm differences are documented in algorithm_analysis.md")
        
        # Check that the output format differences are mentioned
        if 'SamHierarchicalMaskGenerator' in content and '没有 `crop_box`' in content:
            print("✅ Output format differences are documented (no crop_box for hierarchical)")
            return True
        else:
            print("❌ Output format differences not clearly documented")
            return False
    else:
        print("❌ Algorithm analysis documentation incomplete")
        return False

def test_example_script():
    """Test that the example script exists and has the correct structure."""
    
    script_path = '/home/<USER>/shared/DevCode/nanosam/examples/hierarchical_usage.py'
    
    if not os.path.exists(script_path):
        print("❌ hierarchical_usage.py script not found!")
        return False
    
    print("✅ hierarchical_usage.py script exists")
    
    # Read and check the script content
    with open(script_path, 'r') as f:
        content = f.read()
    
    # Check for key components
    checks = [
        ('HierarchicalPredictor import', 'from nanosam.utils.predictor import HierarchicalPredictor'),
        ('high_score_thresh parameter', 'high_score_thresh'),
        ('hierarchical-specific help text', 'hierarchical'),
        ('two-stage description', 'Stage 1'),
        ('adaptive sampling description', 'adaptive'),
    ]
    
    for check_name, check_text in checks:
        if check_text in content:
            print(f"✅ {check_name} found in script")
        else:
            print(f"❌ {check_name} not found in script")
            return False
    
    return True

def main():
    """Run all tests."""
    print("Testing HierarchicalPredictor implementation...")
    print("=" * 60)
    
    tests = [
        ("Class Interface", test_class_interface),
        ("Output Format Documentation", test_output_format),
        ("Example Script", test_example_script),
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing {test_name}:")
        print("-" * 40)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests PASSED! HierarchicalPredictor implementation is correct.")
    else:
        print("❌ Some tests FAILED. Please review the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
